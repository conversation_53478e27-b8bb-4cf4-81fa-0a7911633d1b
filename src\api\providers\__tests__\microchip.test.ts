// npx jest src/api/providers/__tests__/microchip.test.ts

import { MicrochipHandler } from "../microchip";
import { ApiHandlerOptions } from "../../../shared/api";

// Mock fetch globally
global.fetch = jest.fn();

describe("MicrochipHandler", () => {
  let handler: MicrochipHandler;
  let mockFetch: jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
    mockFetch.mockClear();

    const options: ApiHandlerOptions = {
      microchipApiKey: "test-api-key",
      microchipBaseUrl: "https://test-api.example.com",
      microchipAgentMode: true,
    };

    handler = new MicrochipHandler(options);
  });

  describe("Agent Loop Detection", () => {
    it("should prevent infinite loops when agent repeats the same tool", async () => {
      // Mock responses that would cause the agent to repeat the same tool
      mockFetch
        .mockResolvedValueOnce({
          text: () => Promise.resolve("Action: list_files\nAction Input: {\"directory\": \".\"}"),
          ok: true,
          status: 200,
        } as Response)
        .mockResolvedValueOnce({
          text: () => Promise.resolve("Action: list_files\nAction Input: {\"directory\": \"src\"}"),
          ok: true,
          status: 200,
        } as Response);

      const result = await handler.completePrompt("List all files in the project");

      expect(result).toContain("Agent got stuck calling tool 'list_files' repeatedly");
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it("should allow different tools to be called in sequence", async () => {
      // Mock responses with different tools
      mockFetch
        .mockResolvedValueOnce({
          text: () => Promise.resolve("Action: list_files\nAction Input: {\"directory\": \".\"}"),
          ok: true,
          status: 200,
        } as Response)
        .mockResolvedValueOnce({
          text: () => Promise.resolve("Action: read_file\nAction Input: {\"path\": \"package.json\"}"),
          ok: true,
          status: 200,
        } as Response)
        .mockResolvedValueOnce({
          text: () => Promise.resolve("Based on the files, this is a Node.js project."),
          ok: true,
          status: 200,
        } as Response);

      const result = await handler.completePrompt("Analyze the project structure");

      expect(result).toContain("Node.js project");
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it("should handle tool execution errors gracefully", async () => {
      mockFetch
        .mockResolvedValueOnce({
          text: () => Promise.resolve("Action: unknown_tool\nAction Input: {\"param\": \"value\"}"),
          ok: true,
          status: 200,
        } as Response)
        .mockResolvedValueOnce({
          text: () => Promise.resolve("I encountered an error with the unknown tool."),
          ok: true,
          status: 200,
        } as Response);

      const result = await handler.completePrompt("Use an unknown tool");

      expect(result).toContain("error");
    });

    it("should handle invalid JSON in Action Input", async () => {
      mockFetch.mockResolvedValueOnce({
        text: () => Promise.resolve("Action: list_files\nAction Input: {invalid json}"),
        ok: true,
        status: 200,
      } as Response);

      const result = await handler.completePrompt("List files with invalid JSON");

      expect(result).toContain("Could not parse Action Input for tool 'list_files'");
    });

    it("should stop after max steps to prevent infinite loops", async () => {
      // Mock 6 responses with different tools to avoid early loop detection
      const tools = ["searchDocs", "lookupPart", "list_files", "read_file", "write_file"];
      for (let i = 0; i < 6; i++) {
        const toolName = tools[i % tools.length];
        mockFetch.mockResolvedValueOnce({
          text: () => Promise.resolve(`Action: ${toolName}\nAction Input: {"query": "step${i}"}`),
          ok: true,
          status: 200,
        } as Response);
      }

      const result = await handler.completePrompt("Keep searching");

      // Should stop after 5 steps
      expect(mockFetch).toHaveBeenCalledTimes(5);
      expect(result).toBeDefined();
    });
  });

  describe("Tool Execution", () => {
    it("should execute available tools correctly", async () => {
      mockFetch
        .mockResolvedValueOnce({
          text: () => Promise.resolve("Action: list_files\nAction Input: {\"directory\": \"src\"}"),
          ok: true,
          status: 200,
        } as Response)
        .mockResolvedValueOnce({
          text: () => Promise.resolve("Based on the tool result, I can see the files in the src directory."),
          ok: true,
          status: 200,
        } as Response);

      const result = await handler.completePrompt("List files in src directory");

      expect(result).toContain("files in the src directory");
    });

    it("should provide helpful error messages for unknown tools", async () => {
      mockFetch
        .mockResolvedValueOnce({
          text: () => Promise.resolve("Action: nonexistent_tool\nAction Input: {}"),
          ok: true,
          status: 200,
        } as Response)
        .mockResolvedValueOnce({
          text: () => Promise.resolve("I encountered an error with the unknown tool."),
          ok: true,
          status: 200,
        } as Response);

      const result = await handler.completePrompt("Use a nonexistent tool");

      expect(result).toContain("error");
    });
  });

  describe("Prompt Building", () => {
    it("should build agent mode prompts correctly", () => {
      const options: ApiHandlerOptions = {
        microchipApiKey: "test-key",
        microchipAgentMode: true,
      };
      const agentHandler = new MicrochipHandler(options);

      // Access private method for testing
      const buildPrompt = (agentHandler as any).buildPrompt.bind(agentHandler);
      const result = buildPrompt("Test content");

      expect(result).toContain("Use tools when needed—but only once per tool and per step");
      expect(result).toContain("Action: tool_name");
      expect(result).toContain("Test content");
    });

    it("should build regular prompts when agent mode is disabled", () => {
      const options: ApiHandlerOptions = {
        microchipApiKey: "test-key",
        microchipAgentMode: false,
      };
      const regularHandler = new MicrochipHandler(options);

      // Access private method for testing
      const buildPrompt = (regularHandler as any).buildPrompt.bind(regularHandler);
      const result = buildPrompt("Test content");

      expect(result).toContain("Test content");
      expect(result).not.toContain("Action: tool_name");
    });
  });
});
